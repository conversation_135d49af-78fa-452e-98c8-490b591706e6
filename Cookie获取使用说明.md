# Cookie获取功能使用说明

## 功能概述
现在程序已经支持自动打开浏览器获取Cookie功能，无需手动复制粘贴Cookie。

## 使用步骤

### 1. 启动程序
运行 `python 主程序.py` 启动闲鱼采集工具。

### 2. 使用自动Cookie获取
1. 在程序界面中，找到"Cookie设置"页面
2. 点击"刷新Cookie"按钮
3. 程序会弹出确认对话框，询问是否要打开浏览器
4. 点击"是"确认后，程序会自动启动Chrome浏览器
5. 浏览器会自动打开闲鱼网站 (https://2.taobao.com/)

### 3. 登录闲鱼账号
1. 在打开的浏览器窗口中，使用您的淘宝/闲鱼账号登录
2. 确保登录成功，能够正常访问闲鱼页面
3. 登录完成后，回到控制台窗口

### 4. 确认获取Cookie
1. 在控制台中会显示提示："登录完成了吗？(y/n): "
2. 输入 `y` 并按回车键确认登录完成
3. 程序会自动获取Cookie并保存
4. 浏览器会自动关闭

### 5. 验证Cookie
程序会自动验证获取的Cookie是否包含必要的字段：
- `_m_h5_tk` - 必需的令牌字段
- `cookie2` - 认证字段

## 注意事项

### 系统要求
- 已安装 `playwright` 库
- 已安装 Chrome 浏览器驱动

### 安装依赖（如果需要）
```bash
pip install playwright
playwright install chromium
```

### 使用提示
1. **网络连接**：确保网络连接正常，能够访问闲鱼网站
2. **登录状态**：请确保在浏览器中完全登录成功后再确认
3. **取消操作**：如果需要取消操作，在控制台输入 `n` 即可
4. **错误处理**：如果获取失败，程序会显示具体错误信息

### 常见问题

**Q: 浏览器无法启动？**
A: 请确保已安装playwright的浏览器驱动：`playwright install chromium`

**Q: 获取的Cookie无效？**
A: 请确保在浏览器中完全登录成功，并且能够正常访问闲鱼页面

**Q: 程序卡住不动？**
A: 检查控制台是否有输入提示，需要手动输入 'y' 或 'n' 确认

## 技术细节

### Cookie获取范围
程序会获取以下域名的Cookie：
- `.taobao.com`
- `2.taobao.com`
- `.goofish.com`
- `h5api.m.goofish.com`

### 重要Cookie字段
程序特别关注以下重要Cookie字段：
- `_m_h5_tk` - API调用令牌
- `cookie2` - 用户认证
- `_tb_token_` - 淘宝令牌
- `sgcookie` - 安全Cookie
- `unb` - 用户标识
- `uc1`, `uc3`, `uc4` - 用户配置

## 安全说明
- Cookie信息会保存在本地配置文件中
- 请不要将Cookie信息分享给他人
- 定期更新Cookie以确保采集功能正常工作
